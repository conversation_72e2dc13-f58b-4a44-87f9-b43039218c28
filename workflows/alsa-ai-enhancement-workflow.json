{"name": "ALSA AI Content Enhancement", "nodes": [{"id": "enhancement-trigger", "name": "Content Enhancement Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 2.1, "position": [100, 400], "parameters": {"path": "alsa-enhance", "httpMethod": "POST", "responseMode": "lastNode", "responseData": "firstEntryJson"}, "onError": "continueRegularOutput", "alwaysOutputData": true}, {"id": "extract-content", "name": "Extract Content for Enhancement", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [300, 400], "parameters": {"language": "javaScript", "jsCode": "// Extract content and prepare for AI enhancement\nconst payload = items[0].json;\n\n// Extract content from various sources\nlet content = '';\nlet contentType = 'text';\nlet metadata = {};\n\nif (payload.notionPageId) {\n  // Content from Notion page\n  content = payload.content || payload.text || '';\n  contentType = 'notion-page';\n  metadata.pageId = payload.notionPageId;\n} else if (payload.slackMessage) {\n  // Content from Slack\n  content = payload.slackMessage;\n  contentType = 'slack-message';\n  metadata.channelId = payload.channelId;\n  metadata.userId = payload.userId;\n} else {\n  // Direct content\n  content = payload.content || payload.text || '';\n  contentType = 'direct';\n}\n\n// Validate content\nif (!content || content.trim().length < 10) {\n  throw new Error('Content too short for enhancement');\n}\n\n// Prepare enhancement request\nconst enhancementRequest = {\n  originalContent: content.trim(),\n  contentType: contentType,\n  metadata: metadata,\n  enhancementOptions: {\n    generateSummary: payload.generateSummary !== false,\n    extractKeywords: payload.extractKeywords !== false,\n    suggestTags: payload.suggestTags !== false,\n    improveTitle: payload.improveTitle !== false,\n    generateQuestions: payload.generateQuestions !== false,\n    createOutline: payload.createOutline !== false,\n    findRelatedTopics: payload.findRelatedTopics !== false,\n    detectLanguage: payload.detectLanguage !== false,\n    analyzeSentiment: payload.analyzeSentiment !== false,\n    suggestActions: payload.suggestActions !== false\n  },\n  requestId: `enhance_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n};\n\nreturn [{ json: enhancementRequest }];"}}, {"id": "ai-summary-generation", "name": "AI Summary Generation", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [500, 200], "parameters": {"url": "http://ollama:11434/api/generate", "method": "POST", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "model", "value": "llama3"}, {"name": "prompt", "value": "Create a concise 2-3 sentence summary of this content. Focus on the main points and key takeaways. Content: {{ $json.originalContent }}"}, {"name": "stream", "value": false}]}, "options": {"timeout": 30000}}, "continueOnFail": true}, {"id": "ai-keyword-extraction", "name": "AI Keyword Extraction", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [500, 300], "parameters": {"url": "http://ollama:11434/api/generate", "method": "POST", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "model", "value": "llama3"}, {"name": "prompt", "value": "Extract 5-10 key terms and phrases from this content. Return as a JSON array of strings. Content: {{ $node['Extract Content for Enhancement'].json.originalContent }}"}, {"name": "stream", "value": false}, {"name": "format", "value": "json"}]}, "options": {"timeout": 30000}}, "continueOnFail": true}, {"id": "ai-title-improvement", "name": "AI Title Improvement", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [500, 400], "parameters": {"url": "http://ollama:11434/api/generate", "method": "POST", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "model", "value": "llama3"}, {"name": "prompt", "value": "Generate 3 improved, descriptive titles for this content. Make them clear, specific, and engaging. Return as JSON array. Content: {{ $node['Extract Content for Enhancement'].json.originalContent }}"}, {"name": "stream", "value": false}, {"name": "format", "value": "json"}]}, "options": {"timeout": 30000}}, "continueOnFail": true}, {"id": "ai-question-generation", "name": "AI Question Generation", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [500, 500], "parameters": {"url": "http://ollama:11434/api/generate", "method": "POST", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "model", "value": "llama3"}, {"name": "prompt", "value": "Generate 3-5 thoughtful questions that this content raises or could be explored further. Return as JSON array. Content: {{ $node['Extract Content for Enhancement'].json.originalContent }}"}, {"name": "stream", "value": false}, {"name": "format", "value": "json"}]}, "options": {"timeout": 30000}}, "continueOnFail": true}, {"id": "ai-action-suggestions", "name": "AI Action Suggestions", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [500, 600], "parameters": {"url": "http://ollama:11434/api/generate", "method": "POST", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "model", "value": "llama3"}, {"name": "prompt", "value": "Based on this content, suggest 3-5 specific action items or next steps. Return as JSON array of objects with 'action' and 'priority' fields. Content: {{ $node['Extract Content for Enhancement'].json.originalContent }}"}, {"name": "stream", "value": false}, {"name": "format", "value": "json"}]}, "options": {"timeout": 30000}}, "continueOnFail": true}, {"id": "combine-enhancements", "name": "Combine AI Enhancements", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [800, 400], "parameters": {"language": "javaScript", "jsCode": "// Combine all AI enhancement results\nconst originalRequest = $node['Extract Content for Enhancement'].json;\nconst summaryResult = $node['AI Summary Generation'].json;\nconst keywordsResult = $node['AI Keyword Extraction'].json;\nconst titleResult = $node['AI Title Improvement'].json;\nconst questionsResult = $node['AI Question Generation'].json;\nconst actionsResult = $node['AI Action Suggestions'].json;\n\n// Initialize enhancement results\nconst enhancements = {\n  requestId: originalRequest.requestId,\n  originalContent: originalRequest.originalContent,\n  contentType: originalRequest.contentType,\n  metadata: originalRequest.metadata,\n  timestamp: DateTime.now().toISO(),\n  \n  // AI-generated enhancements\n  summary: null,\n  keywords: [],\n  suggestedTitles: [],\n  questions: [],\n  actionItems: [],\n  tags: [],\n  \n  // Analysis results\n  analysis: {\n    wordCount: originalRequest.originalContent.split(/\\s+/).length,\n    characterCount: originalRequest.originalContent.length,\n    estimatedReadingTime: Math.ceil(originalRequest.originalContent.split(/\\s+/).length / 200), // minutes\n    complexity: 'medium' // Will be determined by AI\n  },\n  \n  // Processing status\n  status: 'completed',\n  errors: []\n};\n\n// Process summary\ntry {\n  if (summaryResult && summaryResult.response) {\n    enhancements.summary = summaryResult.response.trim();\n  }\n} catch (error) {\n  enhancements.errors.push('Summary generation failed: ' + error.message);\n}\n\n// Process keywords\ntry {\n  if (keywordsResult && keywordsResult.response) {\n    const keywords = JSON.parse(keywordsResult.response);\n    if (Array.isArray(keywords)) {\n      enhancements.keywords = keywords.slice(0, 10); // Limit to 10\n      // Convert keywords to tags (lowercase, no spaces)\n      enhancements.tags = keywords\n        .map(k => k.toLowerCase().replace(/\\s+/g, '-'))\n        .filter(t => t.length > 2)\n        .slice(0, 8);\n    }\n  }\n} catch (error) {\n  enhancements.errors.push('Keyword extraction failed: ' + error.message);\n}\n\n// Process title suggestions\ntry {\n  if (titleResult && titleResult.response) {\n    const titles = JSON.parse(titleResult.response);\n    if (Array.isArray(titles)) {\n      enhancements.suggestedTitles = titles.slice(0, 5);\n    }\n  }\n} catch (error) {\n  enhancements.errors.push('Title generation failed: ' + error.message);\n}\n\n// Process questions\ntry {\n  if (questionsResult && questionsResult.response) {\n    const questions = JSON.parse(questionsResult.response);\n    if (Array.isArray(questions)) {\n      enhancements.questions = questions.slice(0, 8);\n    }\n  }\n} catch (error) {\n  enhancements.errors.push('Question generation failed: ' + error.message);\n}\n\n// Process action items\ntry {\n  if (actionsResult && actionsResult.response) {\n    const actions = JSON.parse(actionsResult.response);\n    if (Array.isArray(actions)) {\n      enhancements.actionItems = actions.slice(0, 10);\n    }\n  }\n} catch (error) {\n  enhancements.errors.push('Action suggestion failed: ' + error.message);\n}\n\n// Determine content complexity\nconst wordCount = enhancements.analysis.wordCount;\nif (wordCount < 50) {\n  enhancements.analysis.complexity = 'simple';\n} else if (wordCount > 200) {\n  enhancements.analysis.complexity = 'complex';\n}\n\n// Generate enhancement score (0-100)\nlet enhancementScore = 0;\nif (enhancements.summary) enhancementScore += 20;\nif (enhancements.keywords.length > 0) enhancementScore += 15;\nif (enhancements.suggestedTitles.length > 0) enhancementScore += 15;\nif (enhancements.questions.length > 0) enhancementScore += 20;\nif (enhancements.actionItems.length > 0) enhancementScore += 20;\nif (enhancements.tags.length > 0) enhancementScore += 10;\n\nenhancements.enhancementScore = enhancementScore;\n\n// Set status based on errors\nif (enhancements.errors.length > 3) {\n  enhancements.status = 'partial';\n} else if (enhancements.errors.length > 0) {\n  enhancements.status = 'completed_with_warnings';\n}\n\n// Generate insights about the content\nconst insights = [];\n\nif (enhancements.analysis.complexity === 'complex') {\n  insights.push('This is a detailed, complex piece of content that may benefit from breaking into sections.');\n}\n\nif (enhancements.actionItems.length > 5) {\n  insights.push('Multiple action items identified - consider creating a task list.');\n}\n\nif (enhancements.questions.length > 5) {\n  insights.push('This content raises many questions - good for discussion or follow-up research.');\n}\n\nif (enhancements.keywords.length > 8) {\n  insights.push('Rich in keywords - highly searchable and well-structured content.');\n}\n\nenhancements.insights = insights;\n\nreturn [{ json: enhancements }];"}}, {"id": "update-notion-with-enhancements", "name": "Update Notion with Enhancements", "type": "n8n-nodes-base.notion", "typeVersion": 2.2, "position": [1000, 400], "parameters": {"resource": "block", "operation": "append", "blockId": "={{ $json.metadata.pageId }}", "blockUi": [{"type": "divider"}, {"type": "heading_3", "text": "🤖 AI Enhancements"}, {"type": "callout", "text": "{{ $json.summary }}", "icon": "📝"}, {"type": "heading_3", "text": "🏷️ Keywords & Tags"}, {"type": "paragraph", "text": "{{ $json.keywords.join(', ') }}"}, {"type": "heading_3", "text": "❓ Related Questions"}, {"type": "bulleted_list_item", "text": "{{ $json.questions.join('\\n• ') }}"}, {"type": "heading_3", "text": "✅ Suggested Actions"}, {"type": "to_do", "text": "{{ $json.actionItems.map(item => typeof item === 'object' ? item.action : item).join('\\n☐ ') }}"}, {"type": "heading_3", "text": "💡 Alternative Titles"}, {"type": "numbered_list_item", "text": "{{ $json.suggestedTitles.join('\\n1. ') }}"}]}, "continueOnFail": true}, {"id": "send-enhancement-summary", "name": "Send Enhancement Summary", "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [1200, 400], "parameters": {"resource": "message", "operation": "post", "select": "channel", "channelId": "{{ $node['Extract Content for Enhancement'].json.metadata.channelId || $env.SLACK_ENHANCEMENTS_CHANNEL }}", "text": "🤖 *AI Enhancement Complete*\\n\\n**Content:** {{ $json.originalContent.substring(0, 100) }}{{ $json.originalContent.length > 100 ? '...' : '' }}\\n\\n**Enhancement Score:** {{ $json.enhancementScore }}/100\\n**Keywords Found:** {{ $json.keywords.length }}\\n**Action Items:** {{ $json.actionItems.length }}\\n**Questions Generated:** {{ $json.questions.length }}\\n\\n{{ $json.insights.length > 0 ? '**Insights:**\\n• ' + $json.insights.join('\\n• ') : '' }}", "attachments": [{"color": "#36a64f", "title": "Enhancement Summary", "fields": {"item": [{"title": "Word Count", "value": "{{ $json.analysis.wordCount }}", "short": true}, {"title": "Reading Time", "value": "{{ $json.analysis.estimatedReadingTime }} min", "short": true}, {"title": "Complexity", "value": "{{ $json.analysis.complexity }}", "short": true}, {"title": "Status", "value": "{{ $json.status }}", "short": true}]}}]}, "onError": "continueRegularOutput", "retryOnFail": true, "maxTries": 2}], "connections": {"Content Enhancement Trigger": {"main": [[{"node": "Extract Content for Enhancement", "type": "main", "index": 0}]]}, "Extract Content for Enhancement": {"main": [[{"node": "AI Summary Generation", "type": "main", "index": 0}, {"node": "AI Keyword Extraction", "type": "main", "index": 0}, {"node": "AI Title Improvement", "type": "main", "index": 0}, {"node": "AI Question Generation", "type": "main", "index": 0}, {"node": "AI Action Suggestions", "type": "main", "index": 0}]]}, "AI Summary Generation": {"main": [[{"node": "Combine AI Enhancements", "type": "main", "index": 0}]]}, "AI Keyword Extraction": {"main": [[{"node": "Combine AI Enhancements", "type": "main", "index": 0}]]}, "AI Title Improvement": {"main": [[{"node": "Combine AI Enhancements", "type": "main", "index": 0}]]}, "AI Question Generation": {"main": [[{"node": "Combine AI Enhancements", "type": "main", "index": 0}]]}, "AI Action Suggestions": {"main": [[{"node": "Combine AI Enhancements", "type": "main", "index": 0}]]}, "Combine AI Enhancements": {"main": [[{"node": "Update Notion with Enhancements", "type": "main", "index": 0}]]}, "Update Notion with Enhancements": {"main": [[{"node": "Send Enhancement Summary", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1", "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "saveExecutionProgress": true, "saveManualExecutions": true, "timezone": "Asia/Hong_Kong"}}