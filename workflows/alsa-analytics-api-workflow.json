{"name": "ALSA Analytics API", "nodes": [{"id": "analytics-webhook", "name": "Analytics API Endpoint", "type": "n8n-nodes-base.webhook", "typeVersion": 2.1, "position": [100, 300], "parameters": {"path": "alsa-analytics", "httpMethod": "GET", "responseMode": "lastNode", "responseData": "firstEntryJson"}, "onError": "continueRegularOutput", "alwaysOutputData": true}, {"id": "get-all-pages", "name": "Get All ALSA Pages", "type": "n8n-nodes-base.notion", "typeVersion": 2.2, "position": [300, 300], "parameters": {"resource": "databasePage", "operation": "getAll", "databaseId": "{{ $env.NOTION_CATEGORIES_DB_ID }}", "limit": 1000, "sorts": [{"property": "Created", "direction": "descending"}]}}, {"id": "calculate-analytics", "name": "Calculate Analytics", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [500, 300], "parameters": {"language": "javaScript", "jsCode": "// Comprehensive ALSA analytics calculation\nconst pages = items;\nconst now = DateTime.now();\nconst today = now.startOf('day');\nconst weekAgo = now.minus({ days: 7 });\nconst monthAgo = now.minus({ days: 30 });\n\n// Initialize analytics object\nconst analytics = {\n  timestamp: now.toISO(),\n  totalProcessed: pages.length,\n  todayCount: 0,\n  weekCount: 0,\n  monthCount: 0,\n  urgentCount: 0,\n  actionCount: 0,\n  avgWords: 0,\n  sentimentScore: 0,\n  \n  // Distributions\n  dailyTrend: Array(7).fill(0),\n  categories: {},\n  priorities: { high: 0, medium: 0, low: 0 },\n  sources: { slack: 0, email: 0, other: 0 },\n  sentiments: { positive: 0, negative: 0, neutral: 0 },\n  hourlyDistribution: Array(24).fill(0),\n  \n  // Trends\n  topTopics: {},\n  recentActivity: [],\n  performanceMetrics: {\n    avgProcessingTime: 0,\n    successRate: 0,\n    errorRate: 0\n  },\n  \n  // Insights\n  insights: [],\n  recommendations: []\n};\n\n// Track word counts for average calculation\nconst wordCounts = [];\nconst processingTimes = [];\nlet successCount = 0;\nlet errorCount = 0;\n\n// Process each page\npages.forEach(page => {\n  const props = page.json.properties || {};\n  const createdTime = new Date(page.json.created_time);\n  const createdDateTime = DateTime.fromJSDate(createdTime);\n  \n  // Extract properties safely\n  const title = props.Name?.title?.[0]?.plain_text || 'Untitled';\n  const category = props.Category?.select?.name || 'Uncategorized';\n  const priority = props.Priority?.select?.name || 'medium';\n  const source = props.Source?.select?.name || 'slack';\n  const sentiment = props.Sentiment?.select?.name || 'neutral';\n  const tags = props.Tags?.multi_select?.map(tag => tag.name) || [];\n  const wordCount = props.WordCount?.number || 0;\n  const processingTime = props.ProcessingTime?.number || 0;\n  const status = props.Status?.select?.name || 'success';\n  \n  // Time-based counts\n  if (createdDateTime >= today) {\n    analytics.todayCount++;\n  }\n  if (createdDateTime >= weekAgo) {\n    analytics.weekCount++;\n  }\n  if (createdDateTime >= monthAgo) {\n    analytics.monthCount++;\n  }\n  \n  // Daily trend (last 7 days)\n  const dayIndex = Math.floor(now.diff(createdDateTime, 'days').days);\n  if (dayIndex >= 0 && dayIndex < 7) {\n    analytics.dailyTrend[6 - dayIndex]++;\n  }\n  \n  // Hourly distribution\n  const hour = createdTime.getHours();\n  analytics.hourlyDistribution[hour]++;\n  \n  // Category distribution\n  analytics.categories[category] = (analytics.categories[category] || 0) + 1;\n  \n  // Priority distribution\n  if (priority in analytics.priorities) {\n    analytics.priorities[priority]++;\n  }\n  \n  // Source distribution\n  if (source in analytics.sources) {\n    analytics.sources[source]++;\n  }\n  \n  // Sentiment distribution\n  if (sentiment in analytics.sentiments) {\n    analytics.sentiments[sentiment]++;\n  }\n  \n  // Count urgent and action items\n  if (priority === 'high' || tags.includes('urgent')) {\n    analytics.urgentCount++;\n  }\n  \n  if (tags.includes('action-item')) {\n    analytics.actionCount++;\n  }\n  \n  // Track topics from tags\n  tags.forEach(tag => {\n    analytics.topTopics[tag] = (analytics.topTopics[tag] || 0) + 1;\n  });\n  \n  // Performance metrics\n  if (wordCount > 0) {\n    wordCounts.push(wordCount);\n  }\n  \n  if (processingTime > 0) {\n    processingTimes.push(processingTime);\n  }\n  \n  if (status === 'success') {\n    successCount++;\n  } else {\n    errorCount++;\n  }\n  \n  // Recent activity (last 24 hours)\n  if (createdDateTime >= today) {\n    analytics.recentActivity.push({\n      title: title.substring(0, 50) + (title.length > 50 ? '...' : ''),\n      category,\n      priority,\n      time: createdDateTime.toFormat('HH:mm'),\n      tags: tags.slice(0, 3)\n    });\n  }\n});\n\n// Calculate derived metrics\nanalytics.avgWords = wordCounts.length > 0 \n  ? Math.round(wordCounts.reduce((a, b) => a + b, 0) / wordCounts.length)\n  : 0;\n\nanalytics.sentimentScore = analytics.totalProcessed > 0\n  ? Math.round((analytics.sentiments.positive / analytics.totalProcessed) * 100)\n  : 0;\n\nanalytics.performanceMetrics.avgProcessingTime = processingTimes.length > 0\n  ? Math.round(processingTimes.reduce((a, b) => a + b, 0) / processingTimes.length)\n  : 0;\n\nanalytics.performanceMetrics.successRate = analytics.totalProcessed > 0\n  ? Math.round((successCount / analytics.totalProcessed) * 100)\n  : 100;\n\nanalytics.performanceMetrics.errorRate = analytics.totalProcessed > 0\n  ? Math.round((errorCount / analytics.totalProcessed) * 100)\n  : 0;\n\n// Sort and limit top topics\nanalytics.topTopics = Object.entries(analytics.topTopics)\n  .sort(([,a], [,b]) => b - a)\n  .slice(0, 10)\n  .reduce((obj, [key, value]) => ({ ...obj, [key]: value }), {});\n\n// Sort recent activity by time\nanalytics.recentActivity.sort((a, b) => b.time.localeCompare(a.time));\nanalytics.recentActivity = analytics.recentActivity.slice(0, 20);\n\n// Generate insights\nconst insights = [];\n\nif (analytics.totalProcessed === 0) {\n  insights.push('📊 No content has been processed yet. Start by sending messages to Slack!');\n} else {\n  // Activity insights\n  if (analytics.todayCount > 0) {\n    insights.push(`📈 ${analytics.todayCount} items processed today (${Math.round((analytics.todayCount / analytics.weekCount) * 100)}% of weekly activity)`);\n  }\n  \n  // Priority insights\n  if (analytics.urgentCount > 0) {\n    insights.push(`🚨 ${analytics.urgentCount} urgent items need attention`);\n  }\n  \n  // Category insights\n  const topCategory = Object.entries(analytics.categories)\n    .sort(([,a], [,b]) => b - a)[0];\n  if (topCategory) {\n    insights.push(`📂 Most active category: ${topCategory[0]} (${topCategory[1]} items)`);\n  }\n  \n  // Time insights\n  const peakHour = analytics.hourlyDistribution.indexOf(Math.max(...analytics.hourlyDistribution));\n  if (peakHour >= 0) {\n    insights.push(`⏰ Peak activity hour: ${peakHour}:00`);\n  }\n  \n  // Sentiment insights\n  if (analytics.sentimentScore > 70) {\n    insights.push(`😊 High positive sentiment (${analytics.sentimentScore}%)`);\n  } else if (analytics.sentimentScore < 30) {\n    insights.push(`😟 Low positive sentiment (${analytics.sentimentScore}%) - consider reviewing content`);\n  }\n  \n  // Performance insights\n  if (analytics.performanceMetrics.successRate > 95) {\n    insights.push(`✅ Excellent success rate (${analytics.performanceMetrics.successRate}%)`);\n  } else if (analytics.performanceMetrics.successRate < 90) {\n    insights.push(`⚠️ Success rate needs improvement (${analytics.performanceMetrics.successRate}%)`);\n  }\n  \n  // Growth insights\n  const weeklyGrowth = analytics.weekCount > 0 ? Math.round(((analytics.todayCount * 7) / analytics.weekCount - 1) * 100) : 0;\n  if (weeklyGrowth > 20) {\n    insights.push(`📈 Strong growth trend (+${weeklyGrowth}% projected weekly growth)`);\n  } else if (weeklyGrowth < -20) {\n    insights.push(`📉 Declining activity (-${Math.abs(weeklyGrowth)}% weekly decline)`);\n  }\n}\n\nanalytics.insights = insights;\n\n// Generate recommendations\nconst recommendations = [];\n\nif (analytics.urgentCount > 5) {\n  recommendations.push('Consider setting up urgent item alerts in Slack');\n}\n\nif (analytics.actionCount > 10) {\n  recommendations.push('Create a dedicated action items tracking workflow');\n}\n\nif (analytics.performanceMetrics.errorRate > 10) {\n  recommendations.push('Review and improve error handling in ALSA workflows');\n}\n\nif (Object.keys(analytics.categories).length > 20) {\n  recommendations.push('Consider consolidating categories to improve organization');\n}\n\nif (analytics.avgWords < 50) {\n  recommendations.push('Encourage more detailed content for better AI analysis');\n}\n\nanalytics.recommendations = recommendations;\n\nreturn [{ json: analytics }];"}}, {"id": "format-api-response", "name": "Format API Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [700, 300], "parameters": {"language": "javaScript", "jsCode": "// Format the analytics data for API response\nconst analytics = items[0].json;\n\n// Create API-friendly response\nconst apiResponse = {\n  status: 'success',\n  timestamp: analytics.timestamp,\n  data: {\n    summary: {\n      totalProcessed: analytics.totalProcessed,\n      todayCount: analytics.todayCount,\n      weekCount: analytics.weekCount,\n      monthCount: analytics.monthCount,\n      urgentCount: analytics.urgentCount,\n      actionCount: analytics.actionCount,\n      avgWords: analytics.avgWords,\n      sentimentScore: analytics.sentimentScore\n    },\n    distributions: {\n      dailyTrend: analytics.dailyTrend,\n      categories: analytics.categories,\n      priorities: analytics.priorities,\n      sources: analytics.sources,\n      sentiments: analytics.sentiments,\n      hourlyDistribution: analytics.hourlyDistribution\n    },\n    trends: {\n      topTopics: analytics.topTopics,\n      recentActivity: analytics.recentActivity\n    },\n    performance: analytics.performanceMetrics,\n    insights: analytics.insights,\n    recommendations: analytics.recommendations\n  },\n  meta: {\n    version: '1.0',\n    generatedBy: 'ALSA Analytics Engine',\n    cacheExpiry: DateTime.now().plus({ minutes: 5 }).toISO()\n  }\n};\n\nreturn [{ json: apiResponse }];"}}], "connections": {"Analytics API Endpoint": {"main": [[{"node": "Get All ALSA Pages", "type": "main", "index": 0}]]}, "Get All ALSA Pages": {"main": [[{"node": "Calculate Analytics", "type": "main", "index": 0}]]}, "Calculate Analytics": {"main": [[{"node": "Format API Response", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1", "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "saveExecutionProgress": true, "saveManualExecutions": true, "timezone": "Asia/Hong_Kong"}}