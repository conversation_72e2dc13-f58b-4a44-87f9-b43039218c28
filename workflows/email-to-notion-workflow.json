{"name": "Email-to-Notion ALSA Integration", "nodes": [{"id": "email-trigger", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.emailReadImap", "typeVersion": 2, "position": [100, 200], "parameters": {"mailbox": "INBOX", "format": "simple", "options": {"allowUnauthorizedCerts": false, "forceReconnect": false, "deleteReadEmails": false}}}, {"id": "filter-important-emails", "name": "Filter Important Emails", "type": "n8n-nodes-base.filter", "typeVersion": 2, "position": [300, 200], "parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json.subject }}", "rightValue": "ALSA:", "operator": {"type": "string", "operation": "startsWith"}}], "combinator": "or"}}}, {"id": "extract-email-content", "name": "Extract Email Content", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [500, 200], "parameters": {"language": "javaScript", "jsCode": "// Extract and clean email content for ALSA processing\nconst email = items[0].json;\n\n// Clean subject line\nlet subject = email.subject || 'Untitled Email';\nif (subject.startsWith('ALSA:')) {\n  subject = subject.substring(5).trim();\n}\n\n// Extract and clean body content\nlet bodyText = email.text || email.html || '';\n\n// Remove common email signatures and footers\nbodyText = bodyText\n  .replace(/^>.*$/gm, '') // Remove quoted text\n  .replace(/--\\s*$/gm, '') // Remove signature separators\n  .replace(/Sent from my \\w+/gi, '') // Remove mobile signatures\n  .replace(/\\[cid:.*?\\]/g, '') // Remove embedded images\n  .replace(/\\s+/g, ' ') // Normalize whitespace\n  .trim();\n\n// Extract metadata\nconst emailMetadata = {\n  from: email.from?.text || email.from,\n  to: email.to?.text || email.to,\n  date: email.date,\n  messageId: email.messageId,\n  hasAttachments: email.attachments && email.attachments.length > 0,\n  attachmentCount: email.attachments ? email.attachments.length : 0,\n  isReply: subject.toLowerCase().includes('re:'),\n  isForward: subject.toLowerCase().includes('fwd:') || subject.toLowerCase().includes('fw:'),\n  priority: email.priority || 'normal'\n};\n\n// Determine urgency from email headers and content\nconst urgencyIndicators = [\n  email.priority === 'high',\n  subject.toLowerCase().includes('urgent'),\n  subject.toLowerCase().includes('asap'),\n  bodyText.toLowerCase().includes('urgent'),\n  bodyText.toLowerCase().includes('emergency')\n];\nconst isUrgent = urgencyIndicators.some(indicator => indicator);\n\n// Create ALSA-compatible payload\nconst alsaPayload = {\n  rawPageContent: `${subject}\\n\\n${bodyText}`,\n  source: 'email',\n  sourceMetadata: emailMetadata,\n  messageMetadata: {\n    type: 'email',\n    isBot: false,\n    hasAttachments: emailMetadata.hasAttachments,\n    isUrgent: isUrgent,\n    isActionItem: bodyText.toLowerCase().includes('action') || bodyText.toLowerCase().includes('todo'),\n    wordCount: bodyText.split(/\\s+/).length,\n    characterCount: bodyText.length\n  },\n  originalPayload: email\n};\n\nreturn [{ json: alsaPayload }];"}}, {"id": "call-alsa-processor", "name": "Call ALSA Processor", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [700, 200], "parameters": {"url": "http://localhost:5678/webhook/alsa-enhanced", "method": "POST", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "text", "value": "{{ $json.rawPageContent }}"}, {"name": "user_id", "value": "email-{{ $json.sourceMetadata.from }}"}, {"name": "channel_id", "value": "email-inbox"}, {"name": "source", "value": "email"}, {"name": "metadata", "value": "{{ $json.sourceMetadata }}"}]}}}, {"id": "send-email-confirmation", "name": "Send Email Confirmation", "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [900, 200], "parameters": {"fromEmail": "{{ $env.ALSA_EMAIL_FROM }}", "toEmail": "={{ $node['Extract Email Content'].json.sourceMetadata.from }}", "subject": "ALSA: Email processed - {{ $node['Extract Email Content'].json.sourceMetadata.subject }}", "message": "Your email has been successfully processed by ALSA and added to Notion.\\n\\nOriginal Subject: {{ $node['Extract Email Content'].json.sourceMetadata.subject }}\\nProcessed: {{ $now }}\\n\\nView in Notion: {{ $json.notionPageUrl || 'Processing...' }}", "options": {}}}], "connections": {"Email Trigger": {"main": [[{"node": "Filter Important Emails", "type": "main", "index": 0}]]}, "Filter Important Emails": {"main": [[{"node": "Extract Email Content", "type": "main", "index": 0}]]}, "Extract Email Content": {"main": [[{"node": "Call ALSA Processor", "type": "main", "index": 0}]]}, "Call ALSA Processor": {"main": [[{"node": "Send Email Confirmation", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1", "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "saveExecutionProgress": true, "saveManualExecutions": true, "timezone": "Asia/Hong_Kong"}}