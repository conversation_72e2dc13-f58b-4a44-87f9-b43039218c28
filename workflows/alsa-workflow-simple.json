{"name": "ALSA Slack-to-Notion Automation", "nodes": [{"id": "webhook-trigger", "name": "Slack Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2.1, "position": [100, 200], "parameters": {"path": "slack-to-notion", "httpMethod": "POST", "responseMode": "lastNode", "responseData": "firstEntryJson"}, "onError": "continueRegularOutput", "alwaysOutputData": true}, {"id": "extract-slack-data", "name": "Extract Slack Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [300, 200], "parameters": {"language": "javaScript", "jsCode": "const slackData = items[0].json; const messageText = slackData.text || ''; return [{ json: { rawPageContent: messageText.trim(), slackChannelId: slackData.channel_id || '' } }];"}}, {"id": "send-response", "name": "Send Response", "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [500, 200], "parameters": {"resource": "message", "operation": "post", "select": "channel", "channelId": "={{ $json.slackChannelId }}", "text": "Message received and processed!"}, "onError": "continueRegularOutput", "retryOnFail": true, "maxTries": 2}], "connections": {"Slack Webhook": {"main": [[{"node": "Extract Slack Data", "type": "main", "index": 0}]]}, "Extract Slack Data": {"main": [[{"node": "Send Response", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1", "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "saveExecutionProgress": true, "saveManualExecutions": true}}