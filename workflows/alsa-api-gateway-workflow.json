{"name": "ALSA API Gateway", "nodes": [{"id": "api-gateway", "name": "API Gateway", "type": "n8n-nodes-base.webhook", "typeVersion": 2.1, "position": [100, 400], "parameters": {"path": "alsa/api/v1", "httpMethod": "GET", "responseMode": "lastNode", "responseData": "firstEntryJson"}, "onError": "continueRegularOutput", "alwaysOutputData": true}, {"id": "authenticate-request", "name": "Authenticate Request", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [300, 400], "parameters": {"language": "javaScript", "jsCode": "// API Authentication and Request Validation\nconst headers = $input.first().json.headers || {};\nconst query = $input.first().json.query || {};\nconst method = $input.first().json.method || 'GET';\nconst path = $input.first().json.path || '';\n\n// Extract API key from headers or query\nconst apiKey = headers['x-api-key'] || headers['authorization']?.replace('Bearer ', '') || query.api_key;\n\n// Validate API key\nconst validApiKeys = [\n  process.env.ALSA_API_KEY,\n  process.env.ALSA_MOBILE_API_KEY,\n  process.env.ALSA_INTEGRATION_API_KEY\n].filter(key => key); // Remove undefined keys\n\nif (!apiKey || !validApiKeys.includes(apiKey)) {\n  return [{\n    json: {\n      error: 'Unauthorized',\n      message: 'Valid API key required',\n      code: 401\n    }\n  }];\n}\n\n// Rate limiting (simple in-memory implementation)\nconst rateLimitKey = `rate_limit_${apiKey}`;\nconst now = Date.now();\nconst windowMs = 60000; // 1 minute\nconst maxRequests = 100; // per minute\n\n// Get or initialize rate limit data\nlet rateLimitData = global[rateLimitKey] || { count: 0, resetTime: now + windowMs };\n\n// Reset if window expired\nif (now > rateLimitData.resetTime) {\n  rateLimitData = { count: 0, resetTime: now + windowMs };\n}\n\n// Check rate limit\nif (rateLimitData.count >= maxRequests) {\n  return [{\n    json: {\n      error: 'Rate Limit Exceeded',\n      message: `Maximum ${maxRequests} requests per minute`,\n      code: 429,\n      resetTime: rateLimitData.resetTime\n    }\n  }];\n}\n\n// Increment counter\nrateLimitData.count++;\nglobal[rateLimitKey] = rateLimitData;\n\n// Parse endpoint and validate\nconst pathParts = path.split('/').filter(p => p);\nconst endpoint = pathParts[pathParts.length - 1] || 'status';\n\n// Validate endpoint\nconst validEndpoints = [\n  'status', 'analytics', 'content', 'search', 'categories', \n  'enhance', 'summary', 'export', 'health', 'metrics'\n];\n\nif (!validEndpoints.includes(endpoint)) {\n  return [{\n    json: {\n      error: 'Not Found',\n      message: `Endpoint '${endpoint}' not found`,\n      code: 404,\n      availableEndpoints: validEndpoints\n    }\n  }];\n}\n\n// Prepare authenticated request\nconst authenticatedRequest = {\n  endpoint: endpoint,\n  method: method,\n  query: query,\n  headers: headers,\n  apiKey: apiKey,\n  timestamp: now,\n  rateLimitRemaining: maxRequests - rateLimitData.count,\n  rateLimitReset: rateLimitData.resetTime\n};\n\nreturn [{ json: authenticatedRequest }];"}}, {"id": "route-request", "name": "Route Request", "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [500, 400], "parameters": {"rules": {"rules": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json.endpoint }}", "rightValue": "status", "operator": {"type": "string", "operation": "equals"}}]}, "renameOutput": true, "outputKey": "status"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json.endpoint }}", "rightValue": "analytics", "operator": {"type": "string", "operation": "equals"}}]}, "renameOutput": true, "outputKey": "analytics"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json.endpoint }}", "rightValue": "content", "operator": {"type": "string", "operation": "equals"}}]}, "renameOutput": true, "outputKey": "content"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json.endpoint }}", "rightValue": "search", "operator": {"type": "string", "operation": "equals"}}]}, "renameOutput": true, "outputKey": "search"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json.endpoint }}", "rightValue": "enhance", "operator": {"type": "string", "operation": "equals"}}]}, "renameOutput": true, "outputKey": "enhance"}]}}}, {"id": "status-endpoint", "name": "Status Endpoint", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [700, 200], "parameters": {"language": "javaScript", "jsCode": "// ALSA API Status Endpoint\nconst request = items[0].json;\n\nconst status = {\n  service: 'ALSA API',\n  version: '1.0.0',\n  status: 'operational',\n  timestamp: DateTime.now().toISO(),\n  uptime: process.uptime ? Math.floor(process.uptime()) : 'unknown',\n  \n  endpoints: {\n    '/status': 'System status and health',\n    '/analytics': 'Usage analytics and metrics',\n    '/content': 'Content management operations',\n    '/search': 'Search across processed content',\n    '/categories': 'Category management',\n    '/enhance': 'AI content enhancement',\n    '/summary': 'Generate content summaries',\n    '/export': 'Export data in various formats',\n    '/health': 'Detailed health check',\n    '/metrics': 'Performance metrics'\n  },\n  \n  features: {\n    'slack-integration': true,\n    'notion-integration': true,\n    'email-integration': true,\n    'ai-enhancement': true,\n    'analytics': true,\n    'multi-language': true,\n    'real-time-processing': true\n  },\n  \n  limits: {\n    rateLimitPerMinute: 100,\n    maxContentLength: 50000,\n    maxBatchSize: 50\n  },\n  \n  rateLimitInfo: {\n    remaining: request.rateLimitRemaining,\n    resetTime: new Date(request.rateLimitReset).toISOString()\n  }\n};\n\nreturn [{ json: status }];"}}, {"id": "analytics-endpoint", "name": "Analytics Endpoint", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [700, 300], "parameters": {"url": "http://localhost:5678/webhook/alsa-analytics", "method": "GET", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-Internal-Request", "value": "true"}]}, "options": {"timeout": 30000}}}, {"id": "content-endpoint", "name": "Content Endpoint", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [700, 400], "parameters": {"language": "javaScript", "jsCode": "// Content Management Endpoint\nconst request = items[0].json;\nconst query = request.query || {};\n\n// Parse query parameters\nconst page = parseInt(query.page) || 1;\nconst limit = Math.min(parseInt(query.limit) || 20, 100); // Max 100 items\nconst category = query.category;\nconst priority = query.priority;\nconst source = query.source;\nconst dateFrom = query.date_from;\nconst dateTo = query.date_to;\nconst search = query.search;\n\n// Simulate content retrieval (replace with actual Notion API call)\nconst mockContent = {\n  pagination: {\n    page: page,\n    limit: limit,\n    total: 1247,\n    totalPages: Math.ceil(1247 / limit),\n    hasNext: page < Math.ceil(1247 / limit),\n    hasPrev: page > 1\n  },\n  \n  filters: {\n    category: category || null,\n    priority: priority || null,\n    source: source || null,\n    dateRange: {\n      from: dateFrom || null,\n      to: dateTo || null\n    },\n    search: search || null\n  },\n  \n  items: Array.from({ length: Math.min(limit, 20) }, (_, i) => ({\n    id: `page_${page}_${i + 1}`,\n    title: `Sample Content Item ${(page - 1) * limit + i + 1}`,\n    category: ['API Development', 'Bug Reports', 'Feature Requests'][i % 3],\n    priority: ['high', 'medium', 'low'][i % 3],\n    source: ['slack', 'email', 'direct'][i % 3],\n    sentiment: ['positive', 'neutral', 'negative'][i % 3],\n    wordCount: 150 + (i * 25),\n    tags: [`tag-${i + 1}`, `category-${i % 3}`],\n    createdAt: DateTime.now().minus({ days: i }).toISO(),\n    updatedAt: DateTime.now().minus({ hours: i }).toISO(),\n    url: `https://notion.so/page_${page}_${i + 1}`,\n    summary: `This is a sample summary for content item ${(page - 1) * limit + i + 1}. It demonstrates the API response format.`\n  })),\n  \n  meta: {\n    generatedAt: DateTime.now().toISO(),\n    processingTime: '45ms',\n    cacheStatus: 'miss'\n  }\n};\n\nreturn [{ json: mockContent }];"}}, {"id": "search-endpoint", "name": "Search Endpoint", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [700, 500], "parameters": {"language": "javaScript", "jsCode": "// Search Endpoint\nconst request = items[0].json;\nconst query = request.query || {};\n\n// Parse search parameters\nconst searchQuery = query.q || query.query || '';\nconst page = parseInt(query.page) || 1;\nconst limit = Math.min(parseInt(query.limit) || 10, 50);\nconst type = query.type || 'all'; // all, content, categories, tags\nconst sortBy = query.sort || 'relevance'; // relevance, date, title\nconst sortOrder = query.order || 'desc';\n\nif (!searchQuery || searchQuery.length < 2) {\n  return [{\n    json: {\n      error: 'Bad Request',\n      message: 'Search query must be at least 2 characters',\n      code: 400\n    }\n  }];\n}\n\n// Simulate search results\nconst searchResults = {\n  query: {\n    text: searchQuery,\n    type: type,\n    sortBy: sortBy,\n    sortOrder: sortOrder\n  },\n  \n  pagination: {\n    page: page,\n    limit: limit,\n    total: 156,\n    totalPages: Math.ceil(156 / limit)\n  },\n  \n  results: {\n    content: Array.from({ length: Math.min(limit, 8) }, (_, i) => ({\n      id: `search_result_${i + 1}`,\n      title: `Search Result ${i + 1} for \"${searchQuery}\"`,\n      snippet: `This content matches your search for \"${searchQuery}\". Here's a relevant excerpt that shows why this item was returned...`,\n      category: 'API Development',\n      priority: 'medium',\n      relevanceScore: 0.95 - (i * 0.1),\n      url: `https://notion.so/search_result_${i + 1}`,\n      createdAt: DateTime.now().minus({ days: i + 1 }).toISO(),\n      highlights: [`...${searchQuery}...`, `related to ${searchQuery}`]\n    })),\n    \n    categories: [\n      { name: 'API Development', count: 23, relevance: 0.9 },\n      { name: 'Documentation', count: 12, relevance: 0.7 }\n    ],\n    \n    tags: [\n      { name: searchQuery.toLowerCase(), count: 45, relevance: 1.0 },\n      { name: `${searchQuery}-related`, count: 23, relevance: 0.8 }\n    ]\n  },\n  \n  suggestions: [\n    `${searchQuery} documentation`,\n    `${searchQuery} examples`,\n    `${searchQuery} best practices`\n  ],\n  \n  meta: {\n    searchTime: '23ms',\n    totalMatches: 156,\n    indexLastUpdated: DateTime.now().minus({ minutes: 5 }).toISO()\n  }\n};\n\nreturn [{ json: searchResults }];"}}, {"id": "enhance-endpoint", "name": "En<PERSON>ce Endpoint", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [700, 600], "parameters": {"url": "http://localhost:5678/webhook/alsa-enhance", "method": "POST", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-Internal-Request", "value": "true"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "content", "value": "={{ $json.query.content || 'No content provided' }}"}, {"name": "options", "value": "={{ $json.query }}"}]}}}, {"id": "format-api-response", "name": "Format API Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 400], "parameters": {"language": "javaScript", "jsCode": "// Format final API response\nconst responseData = items[0].json;\nconst originalRequest = $node['Authenticate Request'].json;\n\n// Check if this is an error response\nif (responseData.error || responseData.code >= 400) {\n  return [{\n    json: {\n      success: false,\n      error: responseData.error || 'Unknown Error',\n      message: responseData.message || 'An error occurred',\n      code: responseData.code || 500,\n      timestamp: DateTime.now().toISO(),\n      requestId: `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n    }\n  }];\n}\n\n// Format successful response\nconst apiResponse = {\n  success: true,\n  data: responseData,\n  meta: {\n    endpoint: originalRequest.endpoint,\n    method: originalRequest.method,\n    timestamp: DateTime.now().toISO(),\n    requestId: `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n    processingTime: `${Date.now() - originalRequest.timestamp}ms`,\n    version: '1.0.0'\n  },\n  rateLimit: {\n    remaining: originalRequest.rateLimitRemaining,\n    resetTime: new Date(originalRequest.rateLimitReset).toISOString(),\n    limit: 100\n  }\n};\n\n// Add pagination info if present\nif (responseData.pagination) {\n  apiResponse.pagination = responseData.pagination;\n}\n\n// Add search meta if present\nif (responseData.query) {\n  apiResponse.query = responseData.query;\n}\n\nreturn [{ json: apiResponse }];"}}], "connections": {"API Gateway": {"main": [[{"node": "Authenticate Request", "type": "main", "index": 0}]]}, "Authenticate Request": {"main": [[{"node": "Route Request", "type": "main", "index": 0}]]}, "Route Request": {"main": [[{"node": "Status Endpoint", "type": "main", "index": 0}], [{"node": "Analytics Endpoint", "type": "main", "index": 0}], [{"node": "Content Endpoint", "type": "main", "index": 0}], [{"node": "Search Endpoint", "type": "main", "index": 0}], [{"node": "En<PERSON>ce Endpoint", "type": "main", "index": 0}]]}, "Status Endpoint": {"main": [[{"node": "Format API Response", "type": "main", "index": 0}]]}, "Analytics Endpoint": {"main": [[{"node": "Format API Response", "type": "main", "index": 0}]]}, "Content Endpoint": {"main": [[{"node": "Format API Response", "type": "main", "index": 0}]]}, "Search Endpoint": {"main": [[{"node": "Format API Response", "type": "main", "index": 0}]]}, "Enhance Endpoint": {"main": [[{"node": "Format API Response", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1", "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "saveExecutionProgress": true, "saveManualExecutions": true, "timezone": "Asia/Hong_Kong"}}