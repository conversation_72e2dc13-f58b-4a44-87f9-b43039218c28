# ALSA Self-Hosted AI Starter Kit

> **ALSA** (Automated Learning & Synthesis Architect) - A comprehensive automation platform that intelligently processes Slack messages and creates organized Notion pages using AI-powered analysis.

## 🎯 Overview

This self-hosted AI starter kit provides a complete automation solution built on n8n, featuring:

- **Intelligent Slack-to-Notion Processing** - Automatically categorizes and organizes Slack messages into structured Notion pages
- **AI-Powered Content Analysis** - Advanced sentiment analysis, priority detection, and content enhancement
- **Multi-Channel Integration** - Support for Slack, Email, and REST API endpoints
- **Real-time Analytics** - Comprehensive dashboards and monitoring
- **Enterprise Security** - Built-in security auditing and monitoring
- **Self-Hosted Infrastructure** - Complete Docker-based deployment with PostgreSQL, Qdrant, and Ollama

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────┐    ┌─────────────────┐
│   Slack/Email   │───▶│  n8n Engine  │───▶│  Notion Pages   │
│   Messages      │    │   + ALSA     │    │  (Organized)    │
└─────────────────┘    └──────────────┘    └─────────────────┘
                              │
                              ▼
                    ┌──────────────────┐
                    │  AI Analysis     │
                    │  • Ollama/LLM    │
                    │  • Qdrant Vector │
                    │  • Analytics     │
                    └──────────────────┘
```

## 📁 Project Structure

```
├── 📂 config/              # Configuration files
│   ├── docker-compose.yml  # Main Docker setup
│   ├── .env                # Environment variables
│   ├── nginx/              # Nginx reverse proxy config
│   └── ssl/                # SSL certificates
├── 📂 workflows/           # n8n Workflow definitions
│   ├── alsa-slack-notion-workflow.json     # Core ALSA engine
│   ├── alsa-enhanced-workflow.json         # AI-enhanced processing
│   ├── email-to-notion-workflow.json       # Email integration
│   ├── alsa-daily-summary-workflow.json    # Analytics & summaries
│   └── ... (9 total workflows)
├── 📂 scripts/             # Deployment & management scripts
│   ├── deploy-alsa-complete-system.sh      # Full system deployment
│   ├── activate-alsa-workflows.sh          # Workflow activation
│   ├── setup-local-https.sh                # HTTPS setup
│   └── ... (8 total scripts)
└── 📂 docs/                # Documentation
    ├── ALSA_SETUP_GUIDE.md                 # Complete setup guide
    ├── ALSA_COMPLETE_SYSTEM_OVERVIEW.md    # System overview
    ├── HTTPS_SETUP_GUIDE.md                # HTTPS configuration
    └── ... (10 total docs)
```

## 🚀 Quick Start

### Prerequisites

- Docker & Docker Compose
- 8GB+ RAM recommended
- Slack workspace with admin access
- Notion workspace with API access

### 1. Clone and Setup

```bash
git clone <repository-url>
cd self-hosted-ai-starter-kit
```

### 2. Configure Environment

```bash
# Edit environment variables
nano config/.env
# Configure your Slack, Notion, and other API credentials
```

### 3. Start the Infrastructure

```bash
# Start all services (CPU mode)
./docker-compose.sh --profile cpu up -d

# For GPU support (if available)
./docker-compose.sh --profile gpu up -d

# Alternative: Use docker-compose directly
docker-compose -f config/docker-compose.yml --profile cpu up -d
```

### 4. Deploy ALSA Workflows

```bash
# Navigate to scripts directory
cd scripts

# Deploy complete ALSA system
./deploy-alsa-complete-system.sh

# Or deploy basic ALSA only
./deploy-alsa-workflow.sh

# Return to root directory
cd ..
```

### 5. Activate Workflows

```bash
# Navigate to scripts directory and activate workflows
cd scripts && ./activate-alsa-workflows.sh && cd ..
```

## 🔧 Configuration

### Core Services

- **n8n**: `http://localhost:5678` (admin interface)
- **Qdrant**: `http://localhost:6333` (vector database)
- **Ollama**: `http://localhost:11434` (local LLM)
- **PostgreSQL**: `localhost:5432` (database)

### HTTPS Setup

For Slack OAuth integration, HTTPS is required. Choose one option:

1. **Local HTTPS** (Free, recommended for development)
   ```bash
   cd scripts && ./setup-local-https.sh && cd ..
   ```

2. **ngrok** (Free tier, for testing)
   ```bash
   cd scripts && ./restart-ngrok-setup.sh && cd ..
   ```

3. **Production Domain** (Edit `config/.env` with your domain)

## 📊 Available Workflows

| Workflow | Purpose | Status |
|----------|---------|--------|
| `alsa-slack-notion-workflow` | Core Slack-to-Notion automation | ✅ Production Ready |
| `alsa-enhanced-workflow` | AI-powered content enhancement | ✅ Production Ready |
| `email-to-notion-workflow` | Email integration | ✅ Production Ready |
| `alsa-daily-summary-workflow` | Analytics and summaries | ✅ Production Ready |
| `alsa-analytics-api-workflow` | REST API for analytics | ✅ Production Ready |
| `alsa-api-gateway-workflow` | External API gateway | ✅ Production Ready |
| `alsa-security-audit-workflow` | Security monitoring | ✅ Production Ready |
| `alsa-ai-enhancement-workflow` | Content AI enhancement | ✅ Production Ready |
| `alsa-workflow-simple` | Basic test workflow | 🧪 Testing |

## 🔍 Monitoring & Analytics

Access the analytics dashboard at: `docs/alsa-analytics-dashboard.html`

Key metrics include:
- Message processing rates
- AI analysis accuracy
- Notion page creation success
- System performance metrics

## 📚 Documentation

- **[Complete Setup Guide](docs/ALSA_SETUP_GUIDE.md)** - Detailed setup instructions
- **[System Overview](docs/ALSA_COMPLETE_SYSTEM_OVERVIEW.md)** - Architecture and components
- **[HTTPS Setup](docs/HTTPS_SETUP_GUIDE.md)** - SSL/TLS configuration
- **[Webhook Setup](docs/ALSA_WEBHOOK_SETUP_GUIDE.md)** - Slack integration
- **[Implementation Details](docs/README_ALSA_IMPLEMENTATION.md)** - Technical implementation

## 🛠️ Management Scripts

| Script | Purpose |
|--------|---------|
| `deploy-alsa-complete-system.sh` | Deploy all workflows and components |
| `activate-alsa-workflows.sh` | Activate workflows for processing |
| `test-alsa-workflows.sh` | Test workflow functionality |
| `setup-local-https.sh` | Configure local HTTPS |
| `restart-ngrok-setup.sh` | Restart ngrok tunnel |
| `verify-oauth-setup.sh` | Verify Slack OAuth configuration |

## 🔒 Security

- All services run in isolated Docker containers
- Built-in security audit workflow
- HTTPS encryption for all external communications
- Environment-based secret management
- Regular security monitoring and alerts

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

- **Documentation**: Check the `docs/` directory for detailed guides
- **Issues**: Create an issue in the repository
- **Community**: Join our community discussions

---

**Built with ❤️ using n8n, Docker, and AI**
