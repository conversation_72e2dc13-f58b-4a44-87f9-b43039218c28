# HTTPS Setup Guide for n8n Slack OAuth

## Problem
Slack requires HTTPS for OAuth redirect URLs, but your self-hosted n8n instance runs on HTTP (localhost:5678).

## Solutions

### Option 1: Quick Testing with ngrok (Recommended for Development)

#### Step 1: Install ngrok
```bash
# macOS
brew install ngrok

# Or download from https://ngrok.com/
```

#### Step 2: Start ngrok tunnel
```bash
ngrok http 5678
```

This will output something like:
```
Forwarding    https://abc123.ngrok.io -> http://localhost:5678
```

#### Step 3: Update your Slack app configuration
1. Go to your Slack app settings at https://api.slack.com/apps
2. Navigate to "OAuth & Permissions"
3. Set the redirect URL to: `https://abc123.ngrok.io/rest/oauth2-credential/callback`

#### Step 4: Update n8n environment variables
Edit your `.env` file:
```bash
# Uncomment and update these lines with your ngrok URL
N8N_PROTOCOL=https
WEBHOOK_URL=https://abc123.ngrok.io/
N8N_HOST=abc123.ngrok.io
```

#### Step 5: Restart n8n
```bash
docker-compose down
docker-compose --profile cpu up -d
```

### Option 2: Production Setup with Reverse Proxy

#### Add nginx with SSL to docker-compose.yml

Create `nginx.conf`:
```nginx
server {
    listen 80;
    server_name yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl;
    server_name yourdomain.com;

    ssl_certificate /etc/ssl/certs/cert.pem;
    ssl_certificate_key /etc/ssl/private/key.pem;

    location / {
        proxy_pass http://n8n:5678;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

Add nginx service to docker-compose.yml:
```yaml
  nginx:
    image: nginx:alpine
    container_name: n8n-nginx
    networks: ['demo']
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
      - ./ssl:/etc/ssl
    depends_on:
      - n8n
```

### Option 3: Direct HTTPS Configuration

Update docker-compose.yml n8n service:
```yaml
environment:
  - N8N_PROTOCOL=https
  - N8N_HOST=yourdomain.com
  - WEBHOOK_URL=https://yourdomain.com/
  - N8N_SSL_KEY=/certs/key.pem
  - N8N_SSL_CERT=/certs/cert.pem
volumes:
  - ./ssl:/certs
```

## Testing Your Setup

1. **Access n8n via HTTPS**: Visit your HTTPS URL
2. **Test webhook**: Create a simple webhook workflow
3. **Configure Slack OAuth**: Set up your Slack app with the HTTPS redirect URL
4. **Test OAuth flow**: Try connecting Slack credentials in n8n

## Troubleshooting

### Common Issues:
- **Certificate errors**: Ensure SSL certificates are valid
- **Redirect mismatch**: Verify the exact redirect URL in Slack app settings
- **Port conflicts**: Make sure ports 80/443 are available for nginx

### Verification Commands:
```bash
# Check if n8n is accessible via HTTPS
curl -k https://your-domain.com/healthz

# Check SSL certificate
openssl s_client -connect your-domain.com:443

# Check ngrok tunnel status
curl https://your-ngrok-url.ngrok.io/healthz
```

## Next Steps

1. Choose your preferred solution (ngrok for testing, reverse proxy for production)
2. Update your Slack app OAuth settings
3. Test the OAuth flow
4. Deploy your ALSA workflows

## Security Notes

- **ngrok**: Only use for development/testing
- **Production**: Use proper SSL certificates (Let's Encrypt recommended)
- **Firewall**: Ensure proper firewall rules for HTTPS traffic
