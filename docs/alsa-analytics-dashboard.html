<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ALSA Analytics Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .dashboard {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card .icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
        }
        
        .stat-card .value {
            font-size: 2rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .stat-card .label {
            color: #666;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .chart-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .chart-card h3 {
            margin-bottom: 20px;
            color: #333;
            font-size: 1.2rem;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
        }
        
        .insights-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .insights-section h3 {
            margin-bottom: 20px;
            color: #333;
        }
        
        .insight-item {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 5px;
        }
        
        .insight-item .icon {
            margin-right: 10px;
        }
        
        .loading {
            text-align: center;
            padding: 50px;
            color: white;
        }
        
        .error {
            background: #ff6b6b;
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .refresh-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            transition: background 0.3s ease;
            margin-bottom: 20px;
        }
        
        .refresh-btn:hover {
            background: #5a6fd8;
        }
        
        .last-updated {
            color: white;
            text-align: center;
            margin-top: 20px;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="header">
            <h1>🤖 ALSA Analytics Dashboard</h1>
            <p>Real-time insights into your automated knowledge management system</p>
            <button class="refresh-btn" onclick="loadDashboard()">🔄 Refresh Data</button>
        </div>
        
        <div id="loading" class="loading">
            <h2>Loading ALSA analytics...</h2>
        </div>
        
        <div id="error" class="error" style="display: none;">
            <strong>Error:</strong> <span id="error-message"></span>
        </div>
        
        <div id="dashboard-content" style="display: none;">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="icon">📊</div>
                    <div class="value" id="total-processed">0</div>
                    <div class="label">Total Processed</div>
                </div>
                <div class="stat-card">
                    <div class="icon">📈</div>
                    <div class="value" id="today-count">0</div>
                    <div class="label">Today's Items</div>
                </div>
                <div class="stat-card">
                    <div class="icon">🚨</div>
                    <div class="value" id="urgent-count">0</div>
                    <div class="label">Urgent Items</div>
                </div>
                <div class="stat-card">
                    <div class="icon">✅</div>
                    <div class="value" id="action-count">0</div>
                    <div class="label">Action Items</div>
                </div>
                <div class="stat-card">
                    <div class="icon">📝</div>
                    <div class="value" id="avg-words">0</div>
                    <div class="label">Avg Words</div>
                </div>
                <div class="stat-card">
                    <div class="icon">😊</div>
                    <div class="value" id="sentiment-score">0%</div>
                    <div class="label">Positive Sentiment</div>
                </div>
            </div>
            
            <div class="charts-grid">
                <div class="chart-card">
                    <h3>📅 Daily Activity Trend</h3>
                    <div class="chart-container">
                        <canvas id="dailyTrendChart"></canvas>
                    </div>
                </div>
                <div class="chart-card">
                    <h3>📂 Category Distribution</h3>
                    <div class="chart-container">
                        <canvas id="categoryChart"></canvas>
                    </div>
                </div>
                <div class="chart-card">
                    <h3>🎯 Priority Breakdown</h3>
                    <div class="chart-container">
                        <canvas id="priorityChart"></canvas>
                    </div>
                </div>
                <div class="chart-card">
                    <h3>⏰ Hourly Distribution</h3>
                    <div class="chart-container">
                        <canvas id="hourlyChart"></canvas>
                    </div>
                </div>
            </div>
            
            <div class="insights-section">
                <h3>💡 AI-Generated Insights</h3>
                <div id="insights-container">
                    <!-- Insights will be loaded here -->
                </div>
            </div>
        </div>
        
        <div class="last-updated">
            Last updated: <span id="last-updated-time">Never</span>
        </div>
    </div>

    <script>
        // Dashboard configuration
        const config = {
            n8nUrl: 'http://localhost:5678',
            refreshInterval: 300000, // 5 minutes
            apiEndpoints: {
                analytics: '/webhook/alsa-analytics',
                health: '/healthz'
            }
        };

        let charts = {};

        // Initialize dashboard
        async function loadDashboard() {
            try {
                document.getElementById('loading').style.display = 'block';
                document.getElementById('error').style.display = 'none';
                document.getElementById('dashboard-content').style.display = 'none';

                // Fetch analytics data
                const analyticsData = await fetchAnalytics();
                
                // Update statistics
                updateStatistics(analyticsData);
                
                // Update charts
                updateCharts(analyticsData);
                
                // Update insights
                updateInsights(analyticsData);
                
                // Show dashboard
                document.getElementById('loading').style.display = 'none';
                document.getElementById('dashboard-content').style.display = 'block';
                
                // Update timestamp
                document.getElementById('last-updated-time').textContent = new Date().toLocaleString();
                
            } catch (error) {
                console.error('Dashboard load error:', error);
                showError(error.message);
            }
        }

        // Fetch analytics data from n8n
        async function fetchAnalytics() {
            // Simulate API call - replace with actual n8n webhook
            return new Promise((resolve) => {
                setTimeout(() => {
                    resolve({
                        totalProcessed: 1247,
                        todayCount: 23,
                        urgentCount: 5,
                        actionCount: 12,
                        avgWords: 156,
                        sentimentScore: 72,
                        dailyTrend: [15, 23, 18, 31, 25, 19, 23],
                        categories: {
                            'API Development': 45,
                            'Bug Reports': 23,
                            'Feature Requests': 18,
                            'Meeting Notes': 31,
                            'General': 15
                        },
                        priorities: {
                            'High': 15,
                            'Medium': 67,
                            'Low': 18
                        },
                        hourlyDistribution: [2, 1, 0, 0, 1, 3, 8, 15, 22, 18, 16, 12, 8, 6, 9, 11, 7, 4, 3, 2, 1, 1, 0, 1],
                        insights: [
                            'API Development is your most active category this week',
                            'Peak activity occurs between 9-11 AM',
                            'Sentiment analysis shows 72% positive feedback',
                            '5 urgent items require immediate attention',
                            'Average response time has improved by 23%'
                        ]
                    });
                }, 1000);
            });
        }

        // Update statistics cards
        function updateStatistics(data) {
            document.getElementById('total-processed').textContent = data.totalProcessed.toLocaleString();
            document.getElementById('today-count').textContent = data.todayCount;
            document.getElementById('urgent-count').textContent = data.urgentCount;
            document.getElementById('action-count').textContent = data.actionCount;
            document.getElementById('avg-words').textContent = data.avgWords;
            document.getElementById('sentiment-score').textContent = data.sentimentScore + '%';
        }

        // Update all charts
        function updateCharts(data) {
            updateDailyTrendChart(data.dailyTrend);
            updateCategoryChart(data.categories);
            updatePriorityChart(data.priorities);
            updateHourlyChart(data.hourlyDistribution);
        }

        // Daily trend chart
        function updateDailyTrendChart(data) {
            const ctx = document.getElementById('dailyTrendChart').getContext('2d');
            
            if (charts.dailyTrend) {
                charts.dailyTrend.destroy();
            }
            
            charts.dailyTrend = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                    datasets: [{
                        label: 'Items Processed',
                        data: data,
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // Category distribution chart
        function updateCategoryChart(data) {
            const ctx = document.getElementById('categoryChart').getContext('2d');
            
            if (charts.category) {
                charts.category.destroy();
            }
            
            charts.category = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: Object.keys(data),
                    datasets: [{
                        data: Object.values(data),
                        backgroundColor: [
                            '#667eea',
                            '#764ba2',
                            '#f093fb',
                            '#f5576c',
                            '#4facfe'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // Priority breakdown chart
        function updatePriorityChart(data) {
            const ctx = document.getElementById('priorityChart').getContext('2d');
            
            if (charts.priority) {
                charts.priority.destroy();
            }
            
            charts.priority = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: Object.keys(data),
                    datasets: [{
                        label: 'Count',
                        data: Object.values(data),
                        backgroundColor: ['#ff6b6b', '#feca57', '#48dbfb']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // Hourly distribution chart
        function updateHourlyChart(data) {
            const ctx = document.getElementById('hourlyChart').getContext('2d');
            
            if (charts.hourly) {
                charts.hourly.destroy();
            }
            
            const hours = Array.from({length: 24}, (_, i) => i + ':00');
            
            charts.hourly = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: hours,
                    datasets: [{
                        label: 'Activity',
                        data: data,
                        backgroundColor: '#667eea'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // Update insights section
        function updateInsights(data) {
            const container = document.getElementById('insights-container');
            container.innerHTML = '';
            
            const icons = ['💡', '📊', '🎯', '⚡', '🔍'];
            
            data.insights.forEach((insight, index) => {
                const insightDiv = document.createElement('div');
                insightDiv.className = 'insight-item';
                insightDiv.innerHTML = `
                    <span class="icon">${icons[index % icons.length]}</span>
                    ${insight}
                `;
                container.appendChild(insightDiv);
            });
        }

        // Show error message
        function showError(message) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('error').style.display = 'block';
            document.getElementById('error-message').textContent = message;
        }

        // Auto-refresh dashboard
        setInterval(loadDashboard, config.refreshInterval);

        // Load dashboard on page load
        window.addEventListener('load', loadDashboard);
    </script>
</body>
</html>
