# ALSA Slack-to-Notion Automation Setup Guide

## Overview

This guide will help you deploy the ALSA (Automated Learning & Synthesis Architect) workflow that automatically processes Slack messages and creates organized Notion pages with intelligent categorization.

## Prerequisites

1. **Running n8n instance** (from your self-hosted AI starter kit)
2. **Slack workspace** with admin permissions
3. **Notion workspace** with API access
4. **Environment variables** configured

## Step 1: Start Your n8n Instance

```bash
# Navigate to your project directory
cd /path/to/self-hosted-ai-starter-kit

# Start the services
docker-compose --profile cpu up -d

# Access n8n at http://localhost:5678
# Login: Paul / Super-safe-password1
```

## Step 2: Configure Environment Variables

Add these to your `.env` file:

```bash
# Notion Configuration
NOTION_API_TOKEN=your_notion_integration_token
NOTION_INBOX_PAGE_ID=your_inbox_page_id
NOTION_CATEGORIES_DB_ID=your_categories_database_id

# Slack Configuration  
SLACK_BOT_TOKEN=xoxb-your-slack-bot-token
SLACK_SIGNING_SECRET=your_slack_signing_secret
```

## Step 3: Set Up Notion Integration

1. **Create Notion Integration:**
   - Go to https://www.notion.so/my-integrations
   - Click "New integration"
   - Name it "ALSA Bot"
   - Copy the Internal Integration Token

2. **Create Required Pages/Databases:**
   - Create an "Inbox" page for temporary content
   - Create a "Categories" database with these properties:
     - Name (Title)
     - Created (Date)
   - Share both with your integration

3. **Get Page/Database IDs:**
   - Copy the page URL and extract the ID (32-character string)
   - Add to your environment variables

## Step 4: Set Up Slack App

1. **Create Slack App:**
   - Go to https://api.slack.com/apps
   - Click "Create New App" → "From scratch"
   - Name it "ALSA Bot"

2. **Configure Bot Permissions:**
   - Go to "OAuth & Permissions"
   - Add these Bot Token Scopes:
     - `chat:write`
     - `channels:read`
     - `groups:read`
     - `im:read`
     - `mpim:read`

3. **Install App to Workspace:**
   - Click "Install to Workspace"
   - Copy the Bot User OAuth Token

4. **Enable Event Subscriptions:**
   - Go to "Event Subscriptions"
   - Enable Events
   - Request URL: `http://your-n8n-domain:5678/webhook/slack-to-notion`
   - Subscribe to bot events: `message.channels`, `message.groups`, `message.im`, `message.mpim`

## Step 5: Deploy Workflows

### Option A: Simple Test Workflow

```bash
# Import the simple workflow for testing
curl -X POST http://localhost:5678/api/v1/workflows \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d @alsa-workflow-simple.json
```

### Option B: Full ALSA Workflow

```bash
# Import the complete ALSA workflow
curl -X POST http://localhost:5678/api/v1/workflows \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d @alsa-slack-notion-workflow.json
```

## Step 6: Configure Credentials in n8n

1. **Slack Credentials:**
   - Go to Credentials → Add Credential → Slack OAuth2 API
   - Add your Bot Token and Signing Secret

2. **Notion Credentials:**
   - Go to Credentials → Add Credential → Notion API
   - Add your Integration Token

## Step 7: Test the Integration

1. **Activate the workflow** in n8n
2. **Send a test message** in Slack: "This is a test note about API development"
3. **Check Notion** for the created page
4. **Verify Slack response** confirming the note was processed

## Workflow Features

### ALSA Processing Logic

The workflow implements sophisticated content analysis:

- **Content Validation:** Ensures messages have sufficient content (15+ meaningful words)
- **Language Detection:** Supports English and Simplified Chinese
- **Smart Categorization:** Matches content to existing categories or creates new ones
- **Intelligent Titling:** Generates descriptive titles from content
- **Summary Generation:** Creates concise summaries with metadata

### Decision Flow

1. **Insufficient Content:** Flags short/unclear messages for manual review
2. **Existing Category:** Moves content to best-matching existing category
3. **New Category:** Creates new category and organizes content accordingly

### Supported Languages

- **English:** Full processing with intelligent categorization
- **Simplified Chinese:** Native support with Chinese-language responses

## Troubleshooting

### Common Issues

1. **Webhook not receiving data:**
   - Check Slack Event Subscriptions URL
   - Verify n8n is accessible from internet
   - Check firewall/port settings

2. **Notion API errors:**
   - Verify integration has access to pages/databases
   - Check page/database IDs are correct
   - Ensure API token is valid

3. **Slack API errors:**
   - Verify bot token permissions
   - Check bot is added to channels
   - Confirm signing secret is correct

### Debug Mode

Enable debug logging in n8n:
```bash
# Add to docker-compose.yml environment
N8N_LOG_LEVEL=debug
```

## Advanced Configuration

### Custom Categories

Modify the ALSA processing engine to add custom category detection:

```javascript
const topics = {
  'api': 'API Development',
  'database': 'Database Management',
  'auth': 'Authentication & Security',
  'deploy': 'Deployment & DevOps',
  'bug': 'Bug Reports & Issues',
  'feature': 'Feature Requests',
  'meeting': 'Meeting Notes',
  'idea': 'Ideas & Brainstorming',
  // Add your custom categories here
};
```

### Language Customization

Add support for additional languages by modifying the language detection and response generation functions in the ALSA processing engine.

## Security Considerations

1. **Use HTTPS** for webhook endpoints in production
2. **Validate Slack signatures** to prevent unauthorized requests
3. **Limit Notion integration permissions** to required pages only
4. **Rotate API tokens** regularly
5. **Monitor workflow executions** for unusual activity

## Next Steps

1. **Monitor performance** and adjust categorization logic
2. **Add custom categories** based on your team's needs
3. **Implement additional integrations** (email, Teams, etc.)
4. **Set up alerts** for failed executions
5. **Create backup workflows** for critical processes
