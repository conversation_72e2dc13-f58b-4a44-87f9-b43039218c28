#!/bin/bash

# Verification script for Slack OAuth setup
echo "🔐 Verifying Slack OAuth Setup..."
echo ""

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Get current ngrok URL dynamically
NGROK_URL=$(curl -s http://localhost:4040/api/tunnels | grep -o 'https://[^"]*\.ngrok-free\.app' | head -1)
OAUTH_URL="$NGROK_URL/rest/oauth2-credential/callback"

if [ -z "$NGROK_URL" ]; then
    echo -e "${RED}❌ Could not detect ngrok URL. Is ngrok running?${NC}"
    echo "   Run: ngrok http 5678"
    exit 1
fi

echo -e "${BLUE}🔗 Current Setup:${NC}"
echo "   ngrok URL: $NGROK_URL"
echo "   OAuth URL: $OAUTH_URL"
echo ""

# Test 1: ngrok tunnel status
echo "1️⃣ Checking ngrok tunnel..."
if curl -s http://localhost:4040/api/tunnels | grep -q "https"; then
    echo -e "${GREEN}✅ ngrok tunnel is active${NC}"
else
    echo -e "${RED}❌ ngrok tunnel is not active${NC}"
    echo "   Run: ngrok http 5678"
    exit 1
fi

# Test 2: n8n HTTPS access
echo ""
echo "2️⃣ Testing n8n HTTPS access..."
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "$NGROK_URL")
if [ "$HTTP_CODE" = "200" ]; then
    echo -e "${GREEN}✅ n8n accessible via HTTPS${NC}"
else
    echo -e "${RED}❌ n8n not accessible (HTTP $HTTP_CODE)${NC}"
    exit 1
fi

# Test 3: OAuth endpoint
echo ""
echo "3️⃣ Testing OAuth callback endpoint..."
OAUTH_CODE=$(curl -s -o /dev/null -w "%{http_code}" "$OAUTH_URL")
if [ "$OAUTH_CODE" = "200" ] || [ "$OAUTH_CODE" = "400" ]; then
    echo -e "${GREEN}✅ OAuth endpoint accessible${NC}"
else
    echo -e "${RED}❌ OAuth endpoint not accessible (HTTP $OAUTH_CODE)${NC}"
fi

echo ""
echo -e "${YELLOW}📋 Next Steps for Slack OAuth:${NC}"
echo ""
echo "1️⃣ Update your Slack app settings:"
echo "   • Go to: https://api.slack.com/apps"
echo "   • Select your app → OAuth & Permissions"
echo "   • Add redirect URL: $OAUTH_URL"
echo ""
echo "2️⃣ In n8n (https://fce761a72592.ngrok-free.app):"
echo "   • Go to Settings → Credentials"
echo "   • Add new → Slack OAuth2 API"
echo "   • Verify the redirect URL shows: $OAUTH_URL"
echo "   • Enter your Slack app credentials"
echo "   • Complete the OAuth flow"
echo ""
echo "3️⃣ Test the integration:"
echo "   • Create a simple workflow with Slack node"
echo "   • Test sending a message"
echo ""
echo -e "${GREEN}🎉 Your setup is ready for Slack OAuth!${NC}"
