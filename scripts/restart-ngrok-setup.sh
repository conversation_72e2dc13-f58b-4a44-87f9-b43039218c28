#!/bin/bash

# Automated ngrok restart and configuration update script
# This script handles the changing ngrok URLs when restarting

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔄 ngrok Restart & Configuration Update Script${NC}"
echo "=================================================="
echo ""

# Function to kill existing ngrok processes
kill_ngrok() {
    echo -e "${YELLOW}🔪 Stopping existing ngrok processes...${NC}"
    pkill ngrok || true
    sleep 2
    echo -e "${GREEN}✅ ngrok processes stopped${NC}"
}

# Function to start ngrok and get new URL
start_ngrok() {
    echo -e "${YELLOW}🚀 Starting new ngrok tunnel...${NC}"
    
    # Start ngrok in background
    ngrok http 5678 --log=stdout > ngrok.log 2>&1 &
    NGROK_PID=$!
    
    # Wait for ngrok to start
    echo "   Waiting for ngrok to initialize..."
    sleep 5
    
    # Get the new URL from ngrok API
    local max_attempts=10
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s http://localhost:4040/api/tunnels > /dev/null 2>&1; then
            NEW_NGROK_URL=$(curl -s http://localhost:4040/api/tunnels | grep -o 'https://[^"]*\.ngrok-free\.app' | head -1)
            if [ ! -z "$NEW_NGROK_URL" ]; then
                echo -e "${GREEN}✅ ngrok tunnel established${NC}"
                echo -e "${CYAN}🔗 New URL: $NEW_NGROK_URL${NC}"
                return 0
            fi
        fi
        echo "   Attempt $attempt/$max_attempts - waiting for ngrok..."
        sleep 2
        ((attempt++))
    done
    
    echo -e "${RED}❌ Failed to get ngrok URL after $max_attempts attempts${NC}"
    return 1
}

# Function to update .env file
update_env_file() {
    local new_url=$1
    local new_host=$(echo $new_url | sed 's|https://||' | sed 's|/||')
    
    echo -e "${YELLOW}📝 Updating .env file...${NC}"
    
    # Backup current .env
    cp .env .env.backup.$(date +%Y%m%d_%H%M%S)
    
    # Update the .env file
    sed -i.tmp "s|N8N_PROTOCOL=.*|N8N_PROTOCOL=https|g" .env
    sed -i.tmp "s|WEBHOOK_URL=.*|WEBHOOK_URL=${new_url}/|g" .env
    sed -i.tmp "s|N8N_HOST=.*|N8N_HOST=${new_host}|g" .env
    rm .env.tmp
    
    echo -e "${GREEN}✅ .env file updated${NC}"
    echo "   N8N_PROTOCOL=https"
    echo "   WEBHOOK_URL=${new_url}/"
    echo "   N8N_HOST=${new_host}"
}

# Function to restart n8n
restart_n8n() {
    echo -e "${YELLOW}🔄 Restarting n8n with new configuration...${NC}"
    
    docker-compose stop n8n
    sleep 2
    docker-compose up n8n -d
    
    # Wait for n8n to start
    echo "   Waiting for n8n to start..."
    local max_attempts=15
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "${NEW_NGROK_URL}/healthz" > /dev/null 2>&1; then
            echo -e "${GREEN}✅ n8n is ready${NC}"
            return 0
        fi
        echo "   Attempt $attempt/$max_attempts - waiting for n8n..."
        sleep 2
        ((attempt++))
    done
    
    echo -e "${RED}❌ n8n failed to start properly${NC}"
    return 1
}

# Function to display summary
show_summary() {
    local new_url=$1
    local oauth_url="${new_url}/rest/oauth2-credential/callback"
    
    echo ""
    echo -e "${PURPLE}🎉 Setup Complete!${NC}"
    echo "==================="
    echo ""
    echo -e "${CYAN}📋 New URLs:${NC}"
    echo "   n8n HTTPS: $new_url"
    echo "   OAuth Redirect: $oauth_url"
    echo "   ngrok Dashboard: http://localhost:4040"
    echo ""
    echo -e "${YELLOW}⚠️  IMPORTANT: Update your Slack app settings!${NC}"
    echo ""
    echo "1️⃣ Go to: https://api.slack.com/apps"
    echo "2️⃣ Select your app → OAuth & Permissions"
    echo "3️⃣ Update redirect URL to: $oauth_url"
    echo ""
    echo -e "${GREEN}🔧 Quick commands:${NC}"
    echo "   Test setup: ./verify-oauth-setup.sh"
    echo "   Open n8n: open $new_url"
    echo "   Open Slack apps: open https://api.slack.com/apps"
}

# Main execution
main() {
    echo -e "${BLUE}Starting ngrok restart process...${NC}"
    echo ""
    
    # Step 1: Kill existing ngrok
    kill_ngrok
    
    # Step 2: Start new ngrok and get URL
    if ! start_ngrok; then
        echo -e "${RED}❌ Failed to start ngrok${NC}"
        exit 1
    fi
    
    # Step 3: Update .env file
    update_env_file "$NEW_NGROK_URL"
    
    # Step 4: Restart n8n
    if ! restart_n8n; then
        echo -e "${RED}❌ Failed to restart n8n${NC}"
        exit 1
    fi
    
    # Step 5: Show summary
    show_summary "$NEW_NGROK_URL"
    
    # Step 6: Open browser windows
    echo ""
    echo -e "${BLUE}🌐 Opening browser windows...${NC}"
    sleep 2
    open "$NEW_NGROK_URL" 2>/dev/null || true
    open "https://api.slack.com/apps" 2>/dev/null || true
}

# Run main function
main "$@"
