#!/bin/bash

# Setup Local HTTPS with Fixed Domain
# This creates a stable https://n8n.local URL that never changes!

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔒 Setting up Local HTTPS with Fixed Domain${NC}"
echo "=============================================="
echo ""
echo -e "${GREEN}✅ Benefits of this setup:${NC}"
echo "   • Fixed URL: https://n8n.local (never changes!)"
echo "   • No external dependencies"
echo "   • Works offline"
echo "   • Completely free"
echo ""

# Function to check if hosts entry exists
check_hosts_entry() {
    if grep -q "n8n.local" /etc/hosts; then
        echo -e "${GREEN}✅ Hosts entry already exists${NC}"
        return 0
    else
        echo -e "${YELLOW}⚠️  Need to add hosts entry${NC}"
        return 1
    fi
}

# Function to add hosts entry
add_hosts_entry() {
    echo -e "${YELLOW}📝 Adding n8n.local to /etc/hosts...${NC}"
    echo "   This requires sudo password for system file access"
    
    if echo "127.0.0.1 n8n.local" | sudo tee -a /etc/hosts > /dev/null; then
        echo -e "${GREEN}✅ Added n8n.local to hosts file${NC}"
    else
        echo -e "${RED}❌ Failed to add hosts entry${NC}"
        echo "   Please manually add this line to /etc/hosts:"
        echo "   127.0.0.1 n8n.local"
        return 1
    fi
}

# Function to start services
start_services() {
    echo -e "${YELLOW}🚀 Starting services with HTTPS...${NC}"
    
    # Stop any existing services
    docker-compose -f ../config/docker-compose.yml down 2>/dev/null || true

    # Start all services including nginx
    docker-compose -f ../config/docker-compose.yml --profile cpu up -d
    
    echo -e "${GREEN}✅ Services started${NC}"
}

# Function to wait for services
wait_for_services() {
    echo -e "${YELLOW}⏳ Waiting for services to be ready...${NC}"
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -k -s https://n8n.local/healthz > /dev/null 2>&1; then
            echo -e "${GREEN}✅ n8n is ready at https://n8n.local${NC}"
            return 0
        fi
        echo "   Attempt $attempt/$max_attempts - waiting for n8n..."
        sleep 2
        ((attempt++))
    done
    
    echo -e "${RED}❌ Services failed to start properly${NC}"
    return 1
}

# Function to test setup
test_setup() {
    echo -e "${YELLOW}🧪 Testing HTTPS setup...${NC}"
    
    # Test HTTPS connectivity
    if curl -k -s -o /dev/null -w "%{http_code}" https://n8n.local | grep -q "200"; then
        echo -e "${GREEN}✅ HTTPS connectivity: OK${NC}"
    else
        echo -e "${RED}❌ HTTPS connectivity: FAILED${NC}"
        return 1
    fi
    
    # Test OAuth endpoint
    local oauth_url="https://n8n.local/rest/oauth2-credential/callback"
    if curl -k -s -o /dev/null -w "%{http_code}" "$oauth_url" | grep -q -E "200|400|404"; then
        echo -e "${GREEN}✅ OAuth endpoint: OK${NC}"
    else
        echo -e "${RED}❌ OAuth endpoint: FAILED${NC}"
        return 1
    fi
    
    echo -e "${GREEN}✅ All tests passed!${NC}"
}

# Function to show summary
show_summary() {
    echo ""
    echo -e "${PURPLE}🎉 Local HTTPS Setup Complete!${NC}"
    echo "================================"
    echo ""
    echo -e "${CYAN}🔗 Your Fixed URLs (never change):${NC}"
    echo "   n8n Interface: https://n8n.local"
    echo "   OAuth Redirect: https://n8n.local/rest/oauth2-credential/callback"
    echo ""
    echo -e "${YELLOW}📱 Update your Slack app settings:${NC}"
    echo "   1. Go to: https://api.slack.com/apps"
    echo "   2. Select your app → OAuth & Permissions"
    echo "   3. Set redirect URL to: https://n8n.local/rest/oauth2-credential/callback"
    echo "   4. Save settings"
    echo ""
    echo -e "${GREEN}✅ Benefits:${NC}"
    echo "   • URL never changes (even after Docker restarts)"
    echo "   • No external dependencies"
    echo "   • Completely free"
    echo "   • Works offline"
    echo ""
    echo -e "${BLUE}⚠️  Note about SSL Certificate:${NC}"
    echo "   • Your browser will show a security warning (self-signed certificate)"
    echo "   • Click 'Advanced' → 'Proceed to n8n.local (unsafe)'"
    echo "   • This is normal for local development"
    echo "   • Slack OAuth will work fine despite the warning"
    echo ""
    echo -e "${CYAN}🔧 Useful commands:${NC}"
    echo "   • Restart services: docker-compose -f config/docker-compose.yml restart"
    echo "   • View logs: docker-compose -f config/docker-compose.yml logs -f"
    echo "   • Stop services: docker-compose -f config/docker-compose.yml down"
}

# Main execution
main() {
    echo -e "${BLUE}Starting local HTTPS setup...${NC}"
    echo ""
    
    # Check if SSL certificates exist
    if [ ! -f "../config/ssl/cert.pem" ] || [ ! -f "../config/ssl/key.pem" ]; then
        echo -e "${RED}❌ SSL certificates not found${NC}"
        echo "   Please run the SSL certificate generation first"
        exit 1
    fi
    
    # Check hosts entry
    if ! check_hosts_entry; then
        add_hosts_entry
    fi
    
    # Start services
    start_services
    
    # Wait for services to be ready
    if wait_for_services; then
        # Test the setup
        test_setup
        
        # Show summary
        show_summary
        
        # Open browser
        echo ""
        echo -e "${BLUE}🌐 Opening browser...${NC}"
        sleep 2
        open "https://n8n.local" 2>/dev/null || true
        open "https://api.slack.com/apps" 2>/dev/null || true
    else
        echo -e "${RED}❌ Setup failed${NC}"
        exit 1
    fi
}

# Run main function
main "$@"
