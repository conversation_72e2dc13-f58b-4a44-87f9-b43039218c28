#!/bin/bash

# Test script for ALSA workflows
# Tests both the simple and complete ALSA implementations

set -e

# Configuration
N8N_URL="http://localhost:5678"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧪 ALSA Workflow Testing Suite${NC}"
echo "================================"

# Test data - using simple arrays instead of associative arrays for compatibility
TEST_NAMES=("short_content" "valid_content" "chinese_content" "api_content" "meeting_content")
TEST_DATA=(
    '{"text": "test", "channel_id": "test-channel", "user_id": "test-user"}'
    '{"text": "This is a comprehensive test message for ALSA with enough content to pass the validation threshold and demonstrate the intelligent processing capabilities", "channel_id": "test-channel", "user_id": "test-user"}'
    '{"text": "这是一个测试消息，用于验证ALSA系统对中文内容的处理能力。系统应该能够正确识别中文并生成相应的响应。", "channel_id": "test-channel", "user_id": "test-user"}'
    '{"text": "We need to implement a new API endpoint for user authentication. This should include OAuth2 support and proper error handling.", "channel_id": "test-channel", "user_id": "test-user"}'
    '{"text": "Meeting notes from today: Discussed the new feature requirements, assigned tasks to team members, and set deadline for next Friday.", "channel_id": "test-channel", "user_id": "test-user"}'
)

# Function to test a workflow endpoint
test_workflow() {
    local endpoint=$1
    local test_name=$2
    local test_data=$3
    
    echo -e "${YELLOW}Testing ${test_name} on ${endpoint}...${NC}"
    
    local response=$(curl -s -X POST "${N8N_URL}/webhook/${endpoint}" \
        -H "Content-Type: application/json" \
        -d "$test_data" \
        2>/dev/null || echo '{"error": "Request failed"}')
    
    # Check if response is valid JSON
    if echo "$response" | jq . >/dev/null 2>&1; then
        local success=$(echo "$response" | jq -r '.success // false')
        local decision=$(echo "$response" | jq -r '.alsa.decision // "unknown"')
        local language=$(echo "$response" | jq -r '.alsa.language // "unknown"')
        local title=$(echo "$response" | jq -r '.alsa.title // "unknown"')
        
        if [ "$success" = "true" ]; then
            echo -e "${GREEN}  ✅ Success${NC}"
            echo -e "     Decision: ${decision}"
            echo -e "     Language: ${language}"
            echo -e "     Title: ${title}"
        else
            echo -e "${RED}  ❌ Failed${NC}"
            echo -e "     Response: $response"
        fi
    else
        echo -e "${RED}  ❌ Invalid JSON response${NC}"
        echo -e "     Response: $response"
    fi
    
    echo ""
}

# Function to check if n8n is running
check_n8n() {
    echo -e "${YELLOW}Checking n8n status...${NC}"
    if curl -s "${N8N_URL}/healthz" | grep -q "ok"; then
        echo -e "${GREEN}✅ n8n is running${NC}"
        return 0
    else
        echo -e "${RED}❌ n8n is not accessible at ${N8N_URL}${NC}"
        return 1
    fi
}

# Function to test all workflows
test_all_workflows() {
    local workflows=("alsa-simple" "alsa-complete")

    for workflow in "${workflows[@]}"; do
        echo -e "${BLUE}Testing workflow: ${workflow}${NC}"
        echo "----------------------------------------"

        # Loop through test cases using array indices
        for i in "${!TEST_NAMES[@]}"; do
            test_workflow "$workflow" "${TEST_NAMES[$i]}" "${TEST_DATA[$i]}"
        done

        echo ""
    done
}

# Function to show workflow URLs
show_workflow_info() {
    echo -e "${BLUE}📡 Available ALSA Workflows${NC}"
    echo "=========================="
    echo -e "${CYAN}Simple Test Workflow:${NC}"
    echo -e "  URL: ${N8N_URL}/webhook/alsa-simple"
    echo -e "  Purpose: Basic ALSA processing with validation"
    echo ""
    echo -e "${CYAN}Complete Implementation:${NC}"
    echo -e "  URL: ${N8N_URL}/webhook/alsa-complete"
    echo -e "  Purpose: Full ALSA logic with categorization"
    echo ""
}

# Function to run interactive test
interactive_test() {
    echo -e "${BLUE}🎯 Interactive ALSA Test${NC}"
    echo "======================="
    
    echo "Available workflows:"
    echo "1. alsa-simple (Simple)"
    echo "2. alsa-complete (Full)"
    echo ""

    read -p "Select workflow (1 or 2): " workflow_choice

    case $workflow_choice in
        1) endpoint="alsa-simple" ;;
        2) endpoint="alsa-complete" ;;
        *) echo "Invalid choice"; return 1 ;;
    esac
    
    echo ""
    read -p "Enter your test message: " user_message
    
    if [ -z "$user_message" ]; then
        echo "No message provided"
        return 1
    fi
    
    local test_data="{\"text\": \"$user_message\", \"channel_id\": \"interactive-test\", \"user_id\": \"test-user\"}"
    
    echo ""
    echo -e "${YELLOW}Testing your message...${NC}"
    test_workflow "$endpoint" "interactive" "$test_data"
}

# Main execution
main() {
    case "${1:-test}" in
        "test")
            check_n8n || exit 1
            echo ""
            show_workflow_info
            echo ""
            test_all_workflows
            ;;
        "interactive")
            check_n8n || exit 1
            echo ""
            interactive_test
            ;;
        "info")
            show_workflow_info
            ;;
        "help")
            echo "ALSA Workflow Testing Script"
            echo "Usage: $0 [command]"
            echo ""
            echo "Commands:"
            echo "  test        - Run all automated tests (default)"
            echo "  interactive - Run interactive test with custom message"
            echo "  info        - Show workflow information"
            echo "  help        - Show this help"
            ;;
        *)
            echo "Unknown command: $1"
            echo "Use '$0 help' for usage information"
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
